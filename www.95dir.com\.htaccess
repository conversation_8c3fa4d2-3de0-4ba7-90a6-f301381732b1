# 95目录网 - Apache配置文件
# 支持SVG图片显示和其他MIME类型配置

# ==========================================
# MIME类型配置
# ==========================================

# SVG图片支持 - 增强配置
AddType image/svg+xml .svg
AddType image/svg+xml .svgz

# 强制SVG MIME类型 (解决某些服务器配置问题)
<FilesMatch "\.svg$">
    ForceType image/svg+xml
    Header set Content-Type "image/svg+xml; charset=utf-8"
</FilesMatch>

<FilesMatch "\.svgz$">
    ForceType image/svg+xml
    Header set Content-Type "image/svg+xml; charset=utf-8"
    Header set Content-Encoding gzip
</FilesMatch>

# 其他图片格式
AddType image/webp .webp
AddType image/avif .avif

# 字体文件
AddType font/woff .woff
AddType font/woff2 .woff2
AddType application/font-woff .woff
AddType application/font-woff2 .woff2
AddType application/vnd.ms-fontobject .eot
AddType font/truetype .ttf
AddType font/opentype .otf

# 视频文件
AddType video/mp4 .mp4
AddType video/webm .webm
AddType video/ogg .ogv

# 音频文件
AddType audio/mp3 .mp3
AddType audio/ogg .ogg
AddType audio/wav .wav

# 文档文件
AddType application/pdf .pdf
AddType application/json .json

# ==========================================
# 安全配置
# ==========================================

# 防止访问敏感文件
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files>

# 防止访问备份文件
<FilesMatch "\.(bak|backup|old|tmp)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# ==========================================
# 性能优化
# ==========================================

# 启用压缩
<IfModule mod_deflate.c>
    # 压缩文本文件
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
    
    # 压缩SVG
    AddOutputFilterByType DEFLATE image/svg+xml
</IfModule>

# 缓存配置
<IfModule mod_expires.c>
    ExpiresActive On

    # 图片缓存1年（静态资源）
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/ico "access plus 1 year"
    ExpiresByType image/icon "access plus 1 year"

    # CSS和JS缓存1个月
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"

    # 字体缓存1年
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType font/eot "access plus 1 year"

    # HTML文件缓存1小时
    ExpiresByType text/html "access plus 1 hour"

    # XML文件缓存1小时
    ExpiresByType application/xml "access plus 1 hour"
    ExpiresByType text/xml "access plus 1 hour"

    # 其他文档缓存1周
    ExpiresByType application/pdf "access plus 1 week"
    ExpiresByType text/plain "access plus 1 week"

    # 视频文件缓存1年
    ExpiresByType video/mp4 "access plus 1 year"
    ExpiresByType video/webm "access plus 1 year"
</IfModule>

# 设置缓存头
<IfModule mod_headers.c>
    # 静态资源缓存
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|webp)$">
        Header set Cache-Control "public, max-age=31536000"
        Header unset ETag
    </FilesMatch>

    # HTML文件缓存
    <FilesMatch "\.(html|htm|php)$">
        Header set Cache-Control "public, max-age=3600"
    </FilesMatch>

    # 安全头
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    # 移除服务器信息
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# ==========================================
# URL重写规则
# ==========================================

RewriteEngine On

# 修复包含&amp;的URL重定向问题（针对谷歌索引编制问题）
RewriteCond %{QUERY_STRING} ^(.*)&amp;(.*)$
RewriteRule ^(.*)$ /$1?%1&%2 [R=301,L]

# 处理多个&amp;的情况
RewriteCond %{QUERY_STRING} ^(.*)&amp;(.*)&amp;(.*)$
RewriteRule ^(.*)$ /$1?%1&%2&%3 [R=301,L]

# 首页、分类浏览、数据归档、最近更新、排行榜、意见反馈
RewriteRule ^(index|webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap)/?$ index.php?mod=$1 [L]

# VIP相关页面
RewriteRule ^vip/?$ index.php?mod=vip_list [L]
RewriteRule ^vip/list/?$ index.php?mod=vip_list [L]
RewriteRule ^vip/list/(\d+)/?$ index.php?mod=vip_list&page=$1 [L]
RewriteRule ^vip/category/(\d+)/?$ index.php?mod=vip_list&cid=$1 [L]
RewriteRule ^vip/category/(\d+)/(\d+)/?$ index.php?mod=vip_list&cid=$1&page=$2 [L]
RewriteRule ^vip/detail/(\d+)/?$ index.php?mod=vip_detail&id=$1 [L]

# 黑名单相关页面
RewriteRule ^blacklist/?$ index.php?mod=blacklist [L]
RewriteRule ^blacklist/(\d+)/?$ index.php?mod=blacklist&page=$1 [L]
RewriteRule ^blacklist/category/(\d+)/?$ index.php?mod=blacklist&category=$1 [L]
RewriteRule ^blacklist/category/(\d+)/(\d+)/?$ index.php?mod=blacklist&category=$1&page=$2 [L]
RewriteRule ^blacklist/detail/(\d+)/?$ index.php?mod=blacklist_detail&id=$1 [L]

# 待审核相关页面
RewriteRule ^pending/?$ index.php?mod=pending [L]
RewriteRule ^pending/(\d+)/?$ index.php?mod=pending&page=$1 [L]
RewriteRule ^pending/category/(\d+)/?$ index.php?mod=pending&cid=$1 [L]
RewriteRule ^pending/category/(\d+)/(\d+)/?$ index.php?mod=pending&cid=$1&page=$2 [L]
RewriteRule ^pending/detail/(\d+)/?$ index.php?mod=pending_detail&id=$1 [L]

# 最近更新
RewriteRule ^update/(\d+)\.html$ index.php?mod=update&days=$1 [L]
RewriteRule ^update/(\d+)-(\d+)\.html$ index.php?mod=update&days=$1&page=$2 [L]

# 数据归档
RewriteRule ^archives/(\d+)\.html$ index.php?mod=archives&date=$1 [L]
RewriteRule ^archives/(\d+)-(\d+)\.html$ index.php?mod=archives&date=$1&page=$2 [L]

# 站内搜索 - SEO友好URL
RewriteRule ^search/(name|url|tags|intro|article)/(.+)-page-(\d+)\.html$ index.php?mod=search&type=$1&query=$2&page=$3 [L]
RewriteRule ^search/(name|url|tags|intro|article)/(.+)\.html$ index.php?mod=search&type=$1&query=$2 [L]
RewriteRule ^search/(.+)-page-(\d+)\.html$ index.php?mod=search&type=name&query=$1&page=$2 [L]
RewriteRule ^search/(.+)\.html$ index.php?mod=search&type=name&query=$1 [L]

# 站点详细 - SEO友好URL（包含网站名称和域名）
RewriteRule ^siteinfo/(\d+)-([^-]+)-([^.]+)\.html$ index.php?mod=siteinfo&wid=$1 [L]
# 站点详细 - 兼容原有URL格式
RewriteRule ^siteinfo/(\d+)\.html$ index.php?mod=siteinfo&wid=$1 [L]

# 文章详细 - SEO友好URL（包含文章标题）
RewriteRule ^article/(\d+)-([^.]+)\.html$ index.php?mod=artinfo&aid=$1 [L]
# 文章详细 - 兼容原有URL格式
RewriteRule ^artinfo/(\d+)\.html$ index.php?mod=artinfo&aid=$1 [L]

# 链接详细 - SEO友好URL
RewriteRule ^link/(\d+)-([^.]+)\.html$ index.php?mod=linkinfo&lid=$1 [L]
# 链接详细 - 兼容原有URL格式
RewriteRule ^linkinfo/(\d+)\.html$ index.php?mod=linkinfo&lid=$1 [L]

# 单页
RewriteRule ^diypage/(\d+)\.html$ index.php?mod=diypage&pid=$1 [L]

# RSS
RewriteRule ^rssfeed/(\d+)\.html$ index.php?mod=rssfeed&cid=$1 [L]

# SiteMap
RewriteRule ^sitemap/(\d+)\.html$ index.php?mod=sitemap&cid=$1 [L]

# 分类目录
RewriteRule ^webdir/(.+)/(\d+)\.html$ index.php?mod=webdir&cid=$2 [L]
RewriteRule ^webdir/(.+)/(\d+)-(\d+)\.html$ index.php?mod=webdir&cid=$2&page=$3 [L]
RewriteRule ^article/(.+)/(\d+)\.html$ index.php?mod=article&cid=$2 [L]
RewriteRule ^article/(.+)/(\d+)-(\d+)\.html$ index.php?mod=article&cid=$2&page=$3 [L]

# ==========================================
# SEO优化配置
# ==========================================

# 启用ETags
FileETag MTime Size

# 设置默认字符集
AddDefaultCharset UTF-8

# ==========================================
# 谷歌爬虫友好的重定向配置
# ==========================================

# 检测搜索引擎爬虫，避免对爬虫进行重定向
RewriteCond %{HTTP_USER_AGENT} !bot [NC]
RewriteCond %{HTTP_USER_AGENT} !spider [NC]
RewriteCond %{HTTP_USER_AGENT} !crawler [NC]
RewriteCond %{HTTP_USER_AGENT} !Googlebot [NC]
RewriteCond %{HTTP_USER_AGENT} !Baiduspider [NC]
RewriteCond %{HTTP_USER_AGENT} !bingbot [NC]
RewriteCond %{HTTP_USER_AGENT} !360Spider [NC]
RewriteCond %{HTTP_USER_AGENT} !Sogou [NC]

# 强制HTTPS重定向（仅对非爬虫用户，如果使用HTTPS）
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# 域名规范化（仅对非爬虫用户）
# 选择一：移除www前缀
# RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
# RewriteRule ^(.*)$ https://%1/$1 [R=301,L]
# 选择二：添加www前缀
# RewriteCond %{HTTP_HOST} !^www\. [NC]
# RewriteCond %{HTTP_HOST} !^localhost [NC]
# RewriteRule ^(.*)$ https://www.%{HTTP_HOST}%{REQUEST_URI} [R=301,L]

# 移除尾部斜杠（仅对非爬虫用户，除了目录）
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} (.+)/$
RewriteRule ^(.+)/$ /$1 [R=301,L]

# ==========================================
# 错误页面
# ==========================================

ErrorDocument 404 /404.html
ErrorDocument 403 /404.html
ErrorDocument 500 /404.html
