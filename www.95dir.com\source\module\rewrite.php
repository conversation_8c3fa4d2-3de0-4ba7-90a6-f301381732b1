<?php

/** rewrite output */
function rewrite_output($content)
{
    // 首先将HTML实体编码的&amp;转换为&，确保URL正确性
    $content = str_replace('&amp;', '&', $content);

    $search = array(
        "/href\=\"(\.*\/*)\?mod\=(index|webdir|weblink|article|category|update|archives|top|feedback|link|blacklist)\"/",
        "/href\=\"(\.*\/*)\?mod\=webdir([&]cid\=(\d+))?([&]page\=(\d+))?\"/",
        "/href\=\"(\.*\/*)\?mod\=article([&]cid\=(\d+))?([&]page\=(\d+))?\"/",
        "/href\=\"(\.*\/*)\?mod\=update([&]days\=(\d+))?([&]page\=(\d+))?\"/",
        "/href\=\"(\.*\/*)\?mod\=archives([&]date\=(\d+))?([&]page\=(\d+))?\"/",
        "/href\=\"(\.*\/*)\?mod\=search([&]type\=(.+?))?([&]query\=(.+?))?([&]page\=(\d+))?\"/",
        "/href\=\"(\.*\/*)\?mod\=siteinfo[&]wid\=(\d+)\"/",
        "/href\=\"(\.*\/*)\?mod\=diypage[&]pid\=(\d+)\"/",
        "/href\=\"(\.*\/*)\?mod\=rssfeed([&]type\=(webdir|article))?([&]cid\=(\d+))?\"/",
        "/href\=\"(\.*\/*)\?mod\=sitemap([&]type\=(webdir|article))?([&]cid\=(\d+))?\"/",
    );

    $callbacks = array(
        function($matches) { return rewrite_module($matches[2]); },
        function($matches) { return rewrite_category('webdir', isset($matches[3]) ? $matches[3] : '', isset($matches[5]) ? $matches[5] : ''); },
        function($matches) { return rewrite_category('article', isset($matches[3]) ? $matches[3] : '', isset($matches[5]) ? $matches[5] : ''); },
        function($matches) { return rewrite_update(isset($matches[3]) ? $matches[3] : '', isset($matches[5]) ? $matches[5] : ''); },
        function($matches) { return rewrite_archives(isset($matches[3]) ? $matches[3] : '', isset($matches[5]) ? $matches[5] : ''); },
        function($matches) { return rewrite_search(isset($matches[3]) ? $matches[3] : 'name', isset($matches[5]) ? $matches[5] : '', isset($matches[7]) ? $matches[7] : ''); },
        function($matches) { return rewrite_siteinfo($matches[2]); },
        function($matches) { return rewrite_diypage($matches[2]); },
        function($matches) { return rewrite_rssfeed(isset($matches[3]) ? $matches[3] : '', isset($matches[5]) ? $matches[5] : ''); },
        function($matches) { return rewrite_sitemap(isset($matches[3]) ? $matches[3] : '', isset($matches[5]) ? $matches[5] : ''); },
    );

    foreach ($search as $key => $pattern) {
        $content = preg_replace_callback($pattern, $callbacks[$key], $content);
    }

    // 确保输出的HTML中URL参数使用正确的&符号，而不是&amp;
    // 但在HTML属性中，我们需要保持&amp;的正确编码
    // 只对href属性外的URL进行处理
    $content = preg_replace_callback(
        '/href="([^"]*\?[^"]*)"/',
        function($matches) {
            $url = $matches[1];
            // 确保URL中的参数分隔符是&而不是&amp;
            $url = str_replace('&amp;', '&', $url);
            return 'href="' . $url . '"';
        },
        $content
    );

    return $content;
}

/**
 * 检测是否为搜索引擎爬虫
 */
function is_search_engine_bot() {
    if (!isset($_SERVER['HTTP_USER_AGENT'])) {
        return false;
    }

    $user_agent = strtolower($_SERVER['HTTP_USER_AGENT']);
    $bots = array(
        'googlebot', 'bingbot', 'slurp', 'duckduckbot', 'baiduspider',
        '360spider', 'sogou', 'yandexbot', 'facebookexternalhit',
        'twitterbot', 'linkedinbot', 'whatsapp', 'telegrambot'
    );

    foreach ($bots as $bot) {
        if (strpos($user_agent, $bot) !== false) {
            return true;
        }
    }

    return false;
}

/**
 * 为搜索引擎爬虫优化URL输出
 */
function optimize_urls_for_seo($content) {
    // 如果是搜索引擎爬虫，确保URL格式正确
    if (is_search_engine_bot()) {
        // 将所有&amp;替换为&，确保爬虫能正确解析URL
        $content = preg_replace_callback(
            '/href="([^"]*)"/',
            function($matches) {
                $url = $matches[1];
                // 解码HTML实体
                $url = html_entity_decode($url, ENT_QUOTES, 'UTF-8');
                // 确保URL参数正确
                $url = str_replace('&amp;', '&', $url);
                return 'href="' . htmlspecialchars($url, ENT_QUOTES, 'UTF-8') . '"';
            },
            $content
        );
    }

    return $content;
}

/** module */
function rewrite_module($module)
{
    return 'href="' . get_module_url($module) . '"';
}

/** category */
function rewrite_category($cate_mod, $cate_id, $page)
{
    return 'href="' . get_category_url($cate_mod, $cate_id, $page) . '"';
}

/** update */
function rewrite_update($days, $page)
{
    return 'href="' . get_update_url($days, $page) . '"';
}

/** archives */
function rewrite_archives($date, $page)
{
    return 'href="' . get_archives_url($date, $page) . '"';
}

/** search */
function rewrite_search($type = 'name', $query, $page)
{
    return 'href="' . get_search_url($type, $query, $page) . '"';
}

/** siteinfo */
function rewrite_siteinfo($web_id)
{
    return 'href="' . get_website_url($web_id) . '"';
}

/** diypage */
function rewrite_diypage($page_id)
{
    return 'href="' . get_diypage_url($page_id) . '"';
}

/** rssfeed */
function rewrite_rssfeed($module, $cate_id)
{
    return 'href="' . get_rssfeed_url($module, $cate_id) . '"';
}

/** sitemap */
function rewrite_sitemap($module, $cate_id)
{
    return 'href="' . get_sitemap_url($module, $cate_id) . '"';
}
?>